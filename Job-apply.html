<!doctype html>
<html lang="en">
    <head>
        <!-- Required meta tags -->
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
        <meta name="description" content="Job Details - Ed-admin Career Opportunities">
        <meta name="keywords" content="Ed-admin careers, education software jobs, edtech careers, job details">
        <meta name="robots" content="index, follow">
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">
        
        <!-- Bootstrap Min CSS -->
        <link rel="stylesheet" href="assets/css/bootstrap.min.css">
        <!-- Animate Min CSS -->
        <link rel="stylesheet" href="assets/css/animate.min.css">
        <!-- BoxIcons Min CSS -->
        <link rel="stylesheet" href="assets/css/boxicons.min.css">
        <!-- Owl Carousel Min CSS -->
        <link rel="stylesheet" href="assets/css/owl.carousel.min.css">
        <!-- Odometer Min CSS -->
        <link rel="stylesheet" href="assets/css/odometer.min.css">
        <!-- MeanMenu CSS -->
        <link rel="stylesheet" href="assets/css/meanmenu.css">
        <!-- Magnific Popup Min CSS -->
        <link rel="stylesheet" href="assets/css/magnific-popup.min.css">
        <!-- Style CSS -->
        <link rel="stylesheet" href="assets/css/style.css">
        <!-- Responsive CSS -->
        <link rel="stylesheet" href="assets/css/responsive.css">

        <title>Apply for Job - Ed-admin Careers</title>
        <link rel="icon" type="image/png" href="assets/img/favicon-Edadmin.ico">

        <!-- Custom CSS for spinner animation -->
        <style>
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        </style>
        
        <!-- Global site tag (gtag.js) - Google Analytics -->
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-B7QM9WG2P4"></script>
        <script>
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', 'G-B7QM9WG2P4');
        </script>
    </head>

    <body>
        <span data-menuid="about-Ed-admin" class="d-none"></span>
        <!-- Start PopUps Area -->
            <div data-include="popups/demonow"></div>
            <div data-include="popups/bookdemo"></div>
            <div data-include="popups/downloadnow"></div>
            <div data-include="popups/freedemo"></div>
        <!-- End PopUps Area -->

        <!-- Start Header Area -->
        <div data-include="common/header2"></div>
        <!-- End Header Area -->

        <section style="height: 700px; display: flex; justify-content: center; align-items: center;">
            <div style="height: 500px; width: 500px; background-color: #ffffff; display: flex; flex-direction: column;  align-items: left; gap: 20px;">
                <div>
                    <h2>Apply for this role</h2>
                    <p id="job-title-display">Loading job information...</p>
                </div>
                <form id="job-application-form" enctype="multipart/form-data">
                    <div style="margin-bottom: 20px;">
                        <input style="width: 400px; margin-bottom: 15px;" type="text" name="name" id="name" class="form-control" required placeholder="Your Name">
                        <input style="width: 400px; margin-bottom: 15px;" type="email" name="email" id="email" class="form-control" required placeholder="Your Email">
                        <input style="width: 400px; margin-bottom: 15px;" type="tel" name="linkedin-link" id="linkedin-link" class="form-control" placeholder="LinkedIn link (Optional)">
                    </div>

                    <div style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 8px; font-weight: 500; color: #333;">CV or resume</label>
                        <div style="position: relative; width: 400px;">
                            <input type="file" id="cv-upload" name="cv-upload" accept=".pdf,.doc,.docx" style="position: absolute; opacity: 0; width: 100%; height: 100%; cursor: pointer;" onchange="handleFileUpload(this)" required>
                            <div id="file-upload-display" style="width: 400px; height: 80px; border: 1px solid #ddd; border-radius: 4px; display: flex; align-items: center; padding: 0 12px; background-color: #fff; cursor: pointer; transition: border-color 0.3s;" onclick="document.getElementById('cv-upload').click();">
                                <i class='bx bx-upload' style="margin-right: 8px; color: #666; font-size: 18px;"></i>
                                <span id="file-upload-text" style="color: #666; font-size: 14px;">Click to upload or drag and drop</span>
                            </div>
                        </div>
                        <div style="margin-top: 5px; font-size: 12px; color: #888;">Supported formats: PDF, DOC, DOCX (Max 5MB)</div>
                    </div>

                    <div style="margin-top: 30px;">
                        <button type="submit" id="submit-application" style="width: 400px; height: 50px; background-color: #007bff; color: white; border: none; border-radius: 5px; font-size: 16px; font-weight: 600; cursor: pointer; transition: background-color 0.3s;" onmouseover="this.style.backgroundColor='#0056b3'" onmouseout="this.style.backgroundColor='#007bff'">
                            <span id="submit-text">Submit Application</span>
                            <i id="submit-spinner" class='bx bx-loader-alt' style="display: none; margin-left: 8px; animation: spin 1s linear infinite;"></i>
                        </button>
                    </div>
                </form>
            </div>
            <div style="height: 500px; width: 500px; background-color: #a20000;">1</div>

        </section>

        <!-- Start Footer Area -->
        <div data-include="common/footer"></div>
        <!-- End Footer Area -->

        <div class="go-top"><i class='bx bx-chevron-up'></i></div>

        <script src="/assets/js/cookie.js" type="text/javascript"></script>
        <!-- jQuery Min JS -->
        <script src="assets/js/jquery.min.js"></script>
        <!-- Popper Min JS -->
        <script src="assets/js/popper.min.js"></script>
        <!-- Bootstrap Min JS -->
        <script src="assets/js/bootstrap.min.js"></script>
        <!-- Magnific Popup Min JS -->
        <script src="assets/js/jquery.magnific-popup.min.js"></script>
        <!-- Appear Min JS -->
        <script src="assets/js/jquery.appear.min.js"></script>
        <!-- Odometer Min JS -->
        <script src="assets/js/odometer.min.js"></script>
        <!-- Owl Carousel Min JS -->
        <script src="assets/js/owl.carousel.min.js"></script>
        <!-- MeanMenu JS -->
        <script src="assets/js/jquery.meanmenu.js"></script>
        <!-- WOW Min JS -->
        <script src="assets/js/wow.min.js"></script>
        <!-- Message Conversation JS -->
        <script src="assets/js/conversation.js"></script>
        <!-- AjaxChimp Min JS -->
        <script src="assets/js/jquery.ajaxchimp.min.js"></script>
        <!-- Form Validator Min JS -->
        <script src="assets/js/form-validator.min.js"></script>
        <!-- Contact Form Min JS -->
        <script src="assets/js/contact-form-script.js"></script>
        <!-- Particles Min JS -->
        <script src="assets/js/particles.min.js"></script>
        <script src="assets/js/coustom-particles.js"></script>
        <!-- Main JS -->
        <script src="assets/js/main.js"></script>

        <!-- Job Application JavaScript -->
        <script>
            // API Configuration
            const JOBS_API_URL = 'http://localhost:3001/api/jobs';
            const APPLICATION_API_URL = 'http://localhost:3001/api/applications';

            let currentJob = null;

            // Get job ID from URL parameters
            function getJobIdFromUrl() {
                const urlParams = new URLSearchParams(window.location.search);
                return urlParams.get('jobId');
            }

            // Load job details
            async function loadJobDetails() {
                const jobId = getJobIdFromUrl();

                if (!jobId) {
                    showError('No job ID specified');
                    return;
                }

                try {
                    const response = await fetch(`${JOBS_API_URL}/${jobId}`);

                    if (!response.ok) {
                        throw new Error('Job not found');
                    }

                    currentJob = await response.json();
                    displayJobInfo(currentJob);

                } catch (error) {
                    console.error('Error loading job details:', error);
                    showError('Unable to load job details. Please try again later.');
                }
            }

            // Display job information
            function displayJobInfo(job) {
                document.getElementById('job-title-display').textContent = job.title;
                document.title = `Apply for ${job.title} - Ed-admin Careers`;
            }

            // Show error message
            function showError(message) {
                document.getElementById('job-title-display').textContent = message;
                document.getElementById('job-title-display').style.color = '#dc3545';
            }

            function handleFileUpload(input) {
                const fileUploadText = document.getElementById('file-upload-text');
                const fileUploadDisplay = document.getElementById('file-upload-display');

                console.log('File upload triggered:', input.files);

                if (input.files && input.files[0]) {
                    const file = input.files[0];
                    const fileName = file.name;
                    const fileSize = file.size;
                    const maxSize = 5 * 1024 * 1024; // 5MB in bytes

                    console.log('File details:', {
                        name: fileName,
                        size: fileSize,
                        type: file.type
                    });

                    // Check file size
                    if (fileSize > maxSize) {
                        alert('File size exceeds 5MB limit. Please choose a smaller file.');
                        input.value = '';
                        fileUploadText.textContent = 'Click to upload or drag and drop';
                        fileUploadDisplay.style.borderColor = '#ddd';
                        return;
                    }

                    // Check file type - be more lenient with file type checking
                    const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
                    const fileExtension = fileName.toLowerCase().split('.').pop();
                    const allowedExtensions = ['pdf', 'doc', 'docx'];

                    if (!allowedTypes.includes(file.type) && !allowedExtensions.includes(fileExtension)) {
                        alert('Please upload a PDF, DOC, or DOCX file.');
                        input.value = '';
                        fileUploadText.textContent = 'Click to upload or drag and drop';
                        fileUploadDisplay.style.borderColor = '#ddd';
                        return;
                    }

                    // Update display
                    fileUploadText.textContent = fileName;
                    fileUploadDisplay.style.borderColor = '#007bff';
                    console.log('File upload successful:', fileName);
                } else {
                    fileUploadText.textContent = 'Click to upload or drag and drop';
                    fileUploadDisplay.style.borderColor = '#ddd';
                    console.log('No file selected');
                }
            }

            // Handle form submission
            async function submitApplication(event) {
                event.preventDefault();

                if (!currentJob) {
                    alert('Job information not loaded. Please refresh the page and try again.');
                    return;
                }

                const form = event.target;
                const formData = new FormData(form);

                // Get values directly from FormData
                const name = formData.get('name') ? formData.get('name').trim() : '';
                const email = formData.get('email') ? formData.get('email').trim() : '';
                const linkedinLink = formData.get('linkedin-link') ? formData.get('linkedin-link').trim() : '';
                const cvFile = formData.get('cv-upload');

                // Debug logging
                console.log('FormData entries:');
                for (let [key, value] of formData.entries()) {
                    console.log(key, value);
                }

                console.log('Form validation:', {
                    name: name,
                    email: email,
                    cvFile: cvFile,
                    hasName: !!name,
                    hasEmail: !!email,
                    hasCvFile: !!cvFile && cvFile.size > 0
                });

                if (!name || !email || !cvFile || cvFile.size === 0) {
                    alert(`Please fill in all required fields and upload your CV.\nMissing: ${!name ? 'Name ' : ''}${!email ? 'Email ' : ''}${(!cvFile || cvFile.size === 0) ? 'CV file' : ''}`);
                    return;
                }

                // Add job-specific data to FormData
                formData.append('jobId', currentJob.id);
                formData.append('jobTitle', currentJob.title);
                formData.append('jobDescription', currentJob.description);

                // Rename the file field to match backend expectation
                formData.delete('cv-upload');
                formData.append('cv', cvFile);

                // Show loading state
                const submitButton = document.getElementById('submit-application');
                const submitText = document.getElementById('submit-text');
                const submitSpinner = document.getElementById('submit-spinner');

                submitButton.disabled = true;
                submitText.textContent = 'Submitting...';
                submitSpinner.style.display = 'inline-block';

                try {
                    const response = await fetch(APPLICATION_API_URL, {
                        method: 'POST',
                        body: formData
                    });

                    const result = await response.json();

                    if (response.ok) {
                        alert('Application submitted successfully! We will contact you soon.');
                        // Reset form
                        document.getElementById('job-application-form').reset();
                        document.getElementById('file-upload-text').textContent = 'Click to upload or drag and drop';
                        document.getElementById('file-upload-display').style.borderColor = '#ddd';
                    } else {
                        throw new Error(result.error || 'Failed to submit application');
                    }
                } catch (error) {
                    console.error('Error submitting application:', error);
                    alert('Failed to submit application. Please try again later.');
                } finally {
                    // Reset button state
                    submitButton.disabled = false;
                    submitText.textContent = 'Submit Application';
                    submitSpinner.style.display = 'none';
                }
            }

            // Add drag and drop functionality
            document.addEventListener('DOMContentLoaded', function() {
                // Load job details when page loads
                loadJobDetails();

                // Add form submission handler
                document.getElementById('job-application-form').addEventListener('submit', submitApplication);

                const fileUploadDisplay = document.getElementById('file-upload-display');
                const fileInput = document.getElementById('cv-upload');

                // Prevent default drag behaviors
                ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                    fileUploadDisplay.addEventListener(eventName, preventDefaults, false);
                    document.body.addEventListener(eventName, preventDefaults, false);
                });

                // Highlight drop area when item is dragged over it
                ['dragenter', 'dragover'].forEach(eventName => {
                    fileUploadDisplay.addEventListener(eventName, highlight, false);
                });

                ['dragleave', 'drop'].forEach(eventName => {
                    fileUploadDisplay.addEventListener(eventName, unhighlight, false);
                });

                // Handle dropped files
                fileUploadDisplay.addEventListener('drop', handleDrop, false);

                function preventDefaults(e) {
                    e.preventDefault();
                    e.stopPropagation();
                }

                function highlight(e) {
                    fileUploadDisplay.style.borderColor = '#007bff';
                    fileUploadDisplay.style.backgroundColor = '#f8f9fa';
                }

                function unhighlight(e) {
                    fileUploadDisplay.style.borderColor = '#ddd';
                    fileUploadDisplay.style.backgroundColor = '#fff';
                }

                function handleDrop(e) {
                    const dt = e.dataTransfer;
                    const files = dt.files;

                    if (files.length > 0) {
                        fileInput.files = files;
                        handleFileUpload(fileInput);
                    }
                }
            });
        </script>

    </body>
</html>
