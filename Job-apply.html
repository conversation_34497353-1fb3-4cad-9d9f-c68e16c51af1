<!doctype html>
<html lang="en">
    <head>
        <!-- Required meta tags -->
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
        <meta name="description" content="Job Details - Ed-admin Career Opportunities">
        <meta name="keywords" content="Ed-admin careers, education software jobs, edtech careers, job details">
        <meta name="robots" content="index, follow">
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">
        
        <!-- Bootstrap Min CSS -->
        <link rel="stylesheet" href="assets/css/bootstrap.min.css">
        <!-- Animate Min CSS -->
        <link rel="stylesheet" href="assets/css/animate.min.css">
        <!-- BoxIcons Min CSS -->
        <link rel="stylesheet" href="assets/css/boxicons.min.css">
        <!-- Owl Carousel Min CSS -->
        <link rel="stylesheet" href="assets/css/owl.carousel.min.css">
        <!-- Odometer Min CSS -->
        <link rel="stylesheet" href="assets/css/odometer.min.css">
        <!-- MeanMenu CSS -->
        <link rel="stylesheet" href="assets/css/meanmenu.css">
        <!-- Magnific Popup Min CSS -->
        <link rel="stylesheet" href="assets/css/magnific-popup.min.css">
        <!-- Style CSS -->
        <link rel="stylesheet" href="assets/css/style.css">
        <!-- Responsive CSS -->
        <link rel="stylesheet" href="assets/css/responsive.css">

        <title>Job Details - Ed-admin Careers</title>
        <link rel="icon" type="image/png" href="assets/img/favicon-Edadmin.ico">

        <!-- Keep original template styling - no custom CSS needed -->
        
        <!-- Global site tag (gtag.js) - Google Analytics -->
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-B7QM9WG2P4"></script>
        <script>
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', 'G-B7QM9WG2P4');
        </script>
    </head>

    <body>
        <span data-menuid="about-Ed-admin" class="d-none"></span>
        <!-- Start PopUps Area -->
            <div data-include="popups/demonow"></div>
            <div data-include="popups/bookdemo"></div>
            <div data-include="popups/downloadnow"></div>
            <div data-include="popups/freedemo"></div>
        <!-- End PopUps Area -->

        <!-- Start Header Area -->
        <div data-include="common/header2"></div>
        <!-- End Header Area -->

        <section style="height: 700px; display: flex; justify-content: center; align-items: center;">
            <div style="height: 500px; width: 500px; background-color: #ffffff; display: flex; flex-direction: column;  align-items: left; gap: 20px;">
                <div>
                    <h2>Apply for this role</h2>
                    <p>QA Automation Engineer</p>
                </div>
                <div>
                    <form>
                        <input style="width: 400px; margin-bottom: 20px;" type="text" name="name" id="name" class="form-control" required data-error="Please enter your name" placeholder="Your Name">
                        <div class="help-block with-errors"></div>
                        <input style="width: 400px; margin-bottom: 20px;" type="email" name="email" id="email" class="form-control" required data-error="Please enter your email" placeholder="Your Email">
                        <div class="help-block with-errors"></div>
                        <input style="width: 400px;" type="tel" name="linkedin-link" id="linkedin-link" class="form-control" placeholder="linkedin link (Optional) ">
                    </form>
                    
                </div>
                <div>
                    <form>
                        <label style="display: block; margin-bottom: 8px; font-weight: 500; color: #333;">CV or resume</label>
                        <div style="position: relative; width: 400px;">
                            <input type="file" id="cv-upload" name="cv-upload" accept=".pdf,.doc,.docx" style="position: absolute; opacity: 0; width: 100%; height: 100%; cursor: pointer;" onchange="handleFileUpload(this)">
                            <div id="file-upload-display" style="width: 400px; height: 140px; border: 1px solid #ddd; border-radius: 4px; display: flex; align-items: center; padding: 0 12px; background-color: #fff; cursor: pointer; transition: border-color 0.3s;">
                                
                                <div style="display: flex; flex-direction: column; gap: 4px;">
                                    <span id="file-upload-text" style="color: #666; font-size: 14px;">Click to upload or drag and drop</span>
                                <div>
                                    <svg width="22" height="28" viewBox="0 0 22 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M5.66659 22H16.3333V19.3333H5.66659V22ZM5.66659 16.6666H16.3333V14H5.66659V16.6666ZM2.99992 27.3333C2.26659 27.3333 1.63881 27.0722 1.11659 26.55C0.594363 26.0277 0.333252 25.4 0.333252 24.6666V3.33329C0.333252 2.59996 0.594363 1.97218 1.11659 1.44996C1.63881 0.927737 2.26659 0.666626 2.99992 0.666626H13.6666L21.6666 8.66663V24.6666C21.6666 25.4 21.4055 26.0277 20.8833 26.55C20.361 27.0722 19.7333 27.3333 18.9999 27.3333H2.99992ZM12.3333 9.99996V3.33329H2.99992V24.6666H18.9999V9.99996H12.3333Z" fill="#C2C2C2"/>
                                        </svg>

                                </div>
                                </div>
                            </div>
                        </div>
                      
                    </form>
                </div>
            </div>
            <div style="height: 500px; width: 500px; background-color: #a20000;">1</div>

        </section>

        <!-- Start Footer Area -->
        <div data-include="common/footer"></div>
        <!-- End Footer Area -->

        <div class="go-top"><i class='bx bx-chevron-up'></i></div>

        <script src="/assets/js/cookie.js" type="text/javascript"></script>
        <!-- jQuery Min JS -->
        <script src="assets/js/jquery.min.js"></script>
        <!-- Popper Min JS -->
        <script src="assets/js/popper.min.js"></script>
        <!-- Bootstrap Min JS -->
        <script src="assets/js/bootstrap.min.js"></script>
        <!-- Magnific Popup Min JS -->
        <script src="assets/js/jquery.magnific-popup.min.js"></script>
        <!-- Appear Min JS -->
        <script src="assets/js/jquery.appear.min.js"></script>
        <!-- Odometer Min JS -->
        <script src="assets/js/odometer.min.js"></script>
        <!-- Owl Carousel Min JS -->
        <script src="assets/js/owl.carousel.min.js"></script>
        <!-- MeanMenu JS -->
        <script src="assets/js/jquery.meanmenu.js"></script>
        <!-- WOW Min JS -->
        <script src="assets/js/wow.min.js"></script>
        <!-- Message Conversation JS -->
        <script src="assets/js/conversation.js"></script>
        <!-- AjaxChimp Min JS -->
        <script src="assets/js/jquery.ajaxchimp.min.js"></script>
        <!-- Form Validator Min JS -->
        <script src="assets/js/form-validator.min.js"></script>
        <!-- Contact Form Min JS -->
        <script src="assets/js/contact-form-script.js"></script>
        <!-- Particles Min JS -->
        <script src="assets/js/particles.min.js"></script>
        <script src="assets/js/coustom-particles.js"></script>
        <!-- Main JS -->
        <script src="assets/js/main.js"></script>

        <!-- File Upload JavaScript -->
        <script>
            function handleFileUpload(input) {
                const fileUploadText = document.getElementById('file-upload-text');
                const fileUploadDisplay = document.getElementById('file-upload-display');

                if (input.files && input.files[0]) {
                    const file = input.files[0];
                    const fileName = file.name;
                    const fileSize = file.size;
                    const maxSize = 5 * 1024 * 1024; // 5MB in bytes

                    // Check file size
                    if (fileSize > maxSize) {
                        alert('File size exceeds 5MB limit. Please choose a smaller file.');
                        input.value = '';
                        fileUploadText.textContent = 'Choose file or drag and drop';
                        fileUploadDisplay.style.borderColor = '#ddd';
                        return;
                    }

                    // Check file type
                    const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
                    if (!allowedTypes.includes(file.type)) {
                        alert('Please upload a PDF, DOC, or DOCX file.');
                        input.value = '';
                        fileUploadText.textContent = 'Choose file or drag and drop';
                        fileUploadDisplay.style.borderColor = '#ddd';
                        return;
                    }

                    // Update display
                    fileUploadText.textContent = fileName;
                    fileUploadDisplay.style.borderColor = '#007bff';
                } else {
                    fileUploadText.textContent = 'Choose file or drag and drop';
                    fileUploadDisplay.style.borderColor = '#ddd';
                }
            }

            // Add drag and drop functionality
            document.addEventListener('DOMContentLoaded', function() {
                const fileUploadDisplay = document.getElementById('file-upload-display');
                const fileInput = document.getElementById('cv-upload');

                // Prevent default drag behaviors
                ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                    fileUploadDisplay.addEventListener(eventName, preventDefaults, false);
                    document.body.addEventListener(eventName, preventDefaults, false);
                });

                // Highlight drop area when item is dragged over it
                ['dragenter', 'dragover'].forEach(eventName => {
                    fileUploadDisplay.addEventListener(eventName, highlight, false);
                });

                ['dragleave', 'drop'].forEach(eventName => {
                    fileUploadDisplay.addEventListener(eventName, unhighlight, false);
                });

                // Handle dropped files
                fileUploadDisplay.addEventListener('drop', handleDrop, false);

                function preventDefaults(e) {
                    e.preventDefault();
                    e.stopPropagation();
                }

                function highlight(e) {
                    fileUploadDisplay.style.borderColor = '#007bff';
                    fileUploadDisplay.style.backgroundColor = '#f8f9fa';
                }

                function unhighlight(e) {
                    fileUploadDisplay.style.borderColor = '#ddd';
                    fileUploadDisplay.style.backgroundColor = '#fff';
                }

                function handleDrop(e) {
                    const dt = e.dataTransfer;
                    const files = dt.files;

                    if (files.length > 0) {
                        fileInput.files = files;
                        handleFileUpload(fileInput);
                    }
                }
            });
        </script>

    </body>
</html>
