const express = require('express');
const sqlite3 = require('sqlite3').verbose();
const cors = require('cors');
const path = require('path');
const multer = require('multer');
const nodemailer = require('nodemailer');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// Multer configuration for file uploads
const storage = multer.memoryStorage();
const upload = multer({
    storage: storage,
    limits: {
        fileSize: 5 * 1024 * 1024 // 5MB limit
    },
    fileFilter: (req, file, cb) => {
        const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
        if (allowedTypes.includes(file.mimetype)) {
            cb(null, true);
        } else {
            cb(new Error('Invalid file type. Only PDF, DOC, and DOCX files are allowed.'), false);
        }
    }
});

// Email configuration
const transporter = nodemailer.createTransport({
    service: process.env.EMAIL_SERVICE || 'Gmail',
    auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS
    }
});

// Database setup
const dbPath = path.join(__dirname, 'jobs.db');
const db = new sqlite3.Database(dbPath);

// Create jobs table if it doesn't exist
db.serialize(() => {
    db.run(`CREATE TABLE IF NOT EXISTS jobs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        department TEXT NOT NULL,
        location TEXT NOT NULL,
        type TEXT NOT NULL,
        description TEXT NOT NULL,
        benefits TEXT NOT NULL,
        what_youll_do TEXT NOT NULL,
        what_youll_need TEXT NOT NULL,
        requirements TEXT NOT NULL,
        image_url TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )`);

    // Insert demo data
    const demoJobs = [
        {
            title: 'Senior Software Engineer',
            department: 'Engineering',
            location: 'Remote / San Francisco',
            type: 'Full-time',
            description: 'Join our engineering team to build cutting-edge education management software. You will work on scalable web applications, API development, and help shape the future of educational technology.',
            benefits: 'Competitive salary and equity package• Comprehensive health insurance• Flexible work arrangements• Professional development budget• Annual team retreats• Latest tech equipment',
            what_youll_do: 'Design and develop scalable web applications• Collaborate with cross-functional teams• Mentor junior developers• Participate in code reviews• Contribute to technical architecture decisions• Optimize application performance',
            what_youll_need: 'Strong proficiency in JavaScript, Node.js, and React• Experience with database design and optimization• Knowledge of cloud platforms (AWS/Azure)• Excellent problem-solving skills• Ability to work in an agile environment',
            requirements: 'Bachelor\'s degree in Computer Science or related field, 5+ years of experience with JavaScript, Node.js, React, and database technologies. Experience with cloud platforms preferred.',
            image_url: 'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
        },
        {
            title: 'Product Manager',
            department: 'Product',
            location: 'New York / Remote',
            type: 'Full-time',
            description: 'Lead product strategy and development for our education management platform. Work closely with engineering, design, and customer success teams to deliver exceptional user experiences.',
            benefits: 'Competitive salary• Stock options• Comprehensive health benefits• Flexible PTO• Remote work options• Professional development opportunities• Conference attendance budget',
            what_youll_do: 'Define product roadmap and strategy• Conduct user research and market analysis• Collaborate with engineering and design teams• Manage product launches• Analyze product metrics and user feedback',
            what_youll_need: 'Strong analytical and strategic thinking skills• Experience with product management tools• Excellent communication and leadership abilities• Understanding of EdTech market and user needs',
            requirements: 'MBA or equivalent experience, 3+ years in product management, preferably in EdTech or SaaS. Strong analytical skills and experience with user research and data-driven decision making.',
            image_url: 'https://images.unsplash.com/photo-**********-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
        },
        {
            title: 'UX/UI Designer',
            department: 'Design',
            location: 'London / Remote',
            type: 'Full-time',
            description: 'Create intuitive and engaging user experiences for our education management software. Collaborate with product and engineering teams to design interfaces that delight users.',
            benefits: 'Competitive salary• Health insurance• Flexible working hours• Remote work options• Design tool subscriptions• Creative workspace• Team building events',
            what_youll_do: 'Design user interfaces and experiences• Create wireframes and prototypes• Conduct user testing• Collaborate with product and engineering teams• Maintain design systems and style guides',
            what_youll_need: 'Proficiency in design tools like Figma and Sketch• Strong understanding of UX principles• Experience with user research methods• Excellent visual design skills• Ability to work collaboratively',
            requirements: 'Bachelor\'s degree in Design or related field, 3+ years of UX/UI design experience, proficiency in Figma, Sketch, or similar tools. Portfolio demonstrating strong design thinking and problem-solving skills.',
            image_url: 'https://images.unsplash.com/photo-1581291518857-4e27b48ff24e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
        },
        {
            title: 'Customer Success Manager',
            department: 'Customer Success',
            location: 'Austin / Remote',
            type: 'Full-time',
            description: 'Help educational institutions maximize their success with our platform. Build strong relationships with customers, provide training, and ensure high satisfaction and retention rates.',
            benefits: 'Competitive base salary plus commission• Health and dental insurance• Flexible work schedule• Professional development budget• Customer success tools and training',
            what_youll_do: 'Onboard new customers• Provide ongoing support and training• Monitor customer health metrics• Identify upselling opportunities• Gather customer feedback for product improvements',
            what_youll_need: 'Excellent communication and interpersonal skills• Experience with CRM systems• Understanding of SaaS business models• Problem-solving abilities• Customer-focused mindset',
            requirements: '2+ years in customer success or account management, preferably in EdTech or SaaS. Excellent communication skills and passion for helping customers achieve their goals.',
            image_url: 'https://images.unsplash.com/photo-*************-757bb62b4baf?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
        },
        {
            title: 'DevOps Engineer',
            department: 'Engineering',
            location: 'Remote',
            type: 'Full-time',
            description: 'Build and maintain our cloud infrastructure to ensure reliable, scalable, and secure deployment of our education management platform. Work with cutting-edge DevOps tools and practices.',
            benefits: 'Competitive salary• Equity package• Comprehensive health benefits• Home office setup allowance• Continuous learning budget• Flexible working hours',
            what_youll_do: 'Design and implement CI/CD pipelines• Manage cloud infrastructure• Monitor system performance• Ensure security compliance• Automate deployment processes• Troubleshoot production issues',
            what_youll_need: 'Experience with cloud platforms (AWS/Azure)• Proficiency in containerization and orchestration• Knowledge of infrastructure as code• Strong scripting skills• Understanding of security best practices',
            requirements: 'Bachelor\'s degree in Computer Science or related field, 3+ years of DevOps experience, proficiency with AWS/Azure, Docker, Kubernetes, CI/CD pipelines, and infrastructure as code.',
            image_url: 'https://images.unsplash.com/photo-**********-e076c223a692?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
        },
        {
            title: 'Marketing Specialist',
            department: 'Marketing',
            location: 'Chicago / Remote',
            type: 'Full-time',
            description: 'Drive growth through digital marketing campaigns, content creation, and lead generation. Help educational institutions discover how our platform can transform their operations.',
            benefits: 'Competitive salary• Performance bonuses• Health insurance• Marketing tools and software access• Conference attendance opportunities• Creative workspace',
            what_youll_do: 'Develop and execute digital marketing campaigns• Create engaging content• Manage social media presence• Analyze campaign performance• Generate qualified leads• Collaborate with sales team',
            what_youll_need: 'Experience with digital marketing tools and platforms• Strong writing and content creation skills• Knowledge of SEO/SEM• Analytical mindset• Creativity and attention to detail',
            requirements: 'Bachelor\'s degree in Marketing or related field, 2+ years of digital marketing experience, proficiency with marketing automation tools, SEO/SEM, and content marketing.',
            image_url: 'https://images.unsplash.com/photo-**********-d307ca884978?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
        }
    ];

    // Check if demo data already exists
    db.get("SELECT COUNT(*) as count FROM jobs", (err, row) => {
        if (err) {
            console.error('Error checking job count:', err);
            return;
        }

        if (row.count === 0) {
            console.log('Inserting demo job data...');
            const stmt = db.prepare(`INSERT INTO jobs (title, department, location, type, description, benefits, what_youll_do, what_youll_need, requirements, image_url)
                                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`);

            demoJobs.forEach(job => {
                stmt.run([job.title, job.department, job.location, job.type, job.description, job.benefits, job.what_youll_do, job.what_youll_need, job.requirements, job.image_url]);
            });

            stmt.finalize();
            console.log('Demo job data inserted successfully!');
        } else {
            console.log('Demo job data already exists.');
        }
    });
});

// API Routes
app.get('/api/jobs', (req, res) => {
    db.all("SELECT * FROM jobs ORDER BY created_at DESC", (err, rows) => {
        if (err) {
            console.error('Error fetching jobs:', err);
            res.status(500).json({ error: 'Internal server error' });
            return;
        }
        res.json(rows);
    });
});

app.get('/api/jobs/:id', (req, res) => {
    const jobId = req.params.id;
    db.get("SELECT * FROM jobs WHERE id = ?", [jobId], (err, row) => {
        if (err) {
            console.error('Error fetching job:', err);
            res.status(500).json({ error: 'Internal server error' });
            return;
        }
        if (!row) {
            res.status(404).json({ error: 'Job not found' });
            return;
        }
        res.json(row);
    });
});

// Admin API Routes for Job Management
app.post('/api/admin/jobs', (req, res) => {
    const { title, department, location, type, description, benefits, what_youll_do, what_youll_need, requirements, image_url } = req.body;

    if (!title || !department || !location || !type || !description || !benefits || !what_youll_do || !what_youll_need || !requirements) {
        return res.status(400).json({ error: 'All fields except image_url are required' });
    }

    const finalImageUrl = image_url || 'https://images.unsplash.com/photo-**********-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80';

    const stmt = db.prepare(`INSERT INTO jobs (title, department, location, type, description, benefits, what_youll_do, what_youll_need, requirements, image_url)
                           VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`);

    stmt.run([title, department, location, type, description, benefits, what_youll_do, what_youll_need, requirements, finalImageUrl], function(err) {
        if (err) {
            console.error('Error creating job:', err);
            res.status(500).json({ error: 'Internal server error' });
            return;
        }
        res.status(201).json({
            id: this.lastID,
            message: 'Job created successfully',
            job: { id: this.lastID, title, department, location, type, description, benefits, what_youll_do, what_youll_need, requirements, image_url: finalImageUrl }
        });
    });

    stmt.finalize();
});

app.put('/api/admin/jobs/:id', (req, res) => {
    const jobId = req.params.id;
    const { title, department, location, type, description, benefits, what_youll_do, what_youll_need, requirements, image_url } = req.body;

    if (!title || !department || !location || !type || !description || !benefits || !what_youll_do || !what_youll_need || !requirements) {
        return res.status(400).json({ error: 'All fields except image_url are required' });
    }

    const finalImageUrl = image_url || 'https://images.unsplash.com/photo-**********-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80';

    const stmt = db.prepare(`UPDATE jobs SET title = ?, department = ?, location = ?, type = ?,
                           description = ?, benefits = ?, what_youll_do = ?, what_youll_need = ?, requirements = ?, image_url = ? WHERE id = ?`);

    stmt.run([title, department, location, type, description, benefits, what_youll_do, what_youll_need, requirements, finalImageUrl, jobId], function(err) {
        if (err) {
            console.error('Error updating job:', err);
            res.status(500).json({ error: 'Internal server error' });
            return;
        }
        if (this.changes === 0) {
            res.status(404).json({ error: 'Job not found' });
            return;
        }
        res.json({
            message: 'Job updated successfully',
            job: { id: jobId, title, department, location, type, description, benefits, what_youll_do, what_youll_need, requirements, image_url: finalImageUrl }
        });
    });

    stmt.finalize();
});

app.delete('/api/admin/jobs/:id', (req, res) => {
    const jobId = req.params.id;

    const stmt = db.prepare('DELETE FROM jobs WHERE id = ?');

    stmt.run([jobId], function(err) {
        if (err) {
            console.error('Error deleting job:', err);
            res.status(500).json({ error: 'Internal server error' });
            return;
        }
        if (this.changes === 0) {
            res.status(404).json({ error: 'Job not found' });
            return;
        }
        res.json({ message: 'Job deleted successfully' });
    });

    stmt.finalize();
});

// Job Application endpoint
app.post('/api/applications', upload.single('cv'), async (req, res) => {
    try {
        const { name, email, linkedinLink, jobId, jobTitle, jobDescription } = req.body;
        const cvFile = req.file;

        // Validate required fields
        if (!name || !email || !jobId || !jobTitle || !cvFile) {
            return res.status(400).json({ error: 'Missing required fields' });
        }

        // Prepare email content
        const emailSubject = `Job Application: ${jobTitle} - ${name}`;
        const emailHtml = `
            <html>
                <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                    <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                        <h2 style="color: #007bff; border-bottom: 2px solid #007bff; padding-bottom: 10px;">
                            New Job Application Received
                        </h2>

                        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
                            <h3 style="margin-top: 0; color: #495057;">Job Details</h3>
                            <p><strong>Position:</strong> ${jobTitle}</p>
                            <p><strong>Job ID:</strong> ${jobId}</p>
                        </div>

                        <div style="background-color: #fff; padding: 20px; border: 1px solid #dee2e6; border-radius: 5px; margin: 20px 0;">
                            <h3 style="margin-top: 0; color: #495057;">Applicant Information</h3>
                            <p><strong>Name:</strong> ${name}</p>
                            <p><strong>Email:</strong> ${email}</p>
                            ${linkedinLink ? `<p><strong>LinkedIn:</strong> <a href="${linkedinLink}" target="_blank">${linkedinLink}</a></p>` : ''}
                        </div>

                        <div style="background-color: #e9ecef; padding: 20px; border-radius: 5px; margin: 20px 0;">
                            <h3 style="margin-top: 0; color: #495057;">Job Description</h3>
                            <p style="white-space: pre-wrap;">${jobDescription}</p>
                        </div>

                        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6; font-size: 12px; color: #6c757d;">
                            <p>This application was submitted through the Ed-admin careers website.</p>
                            <p>CV/Resume is attached to this email.</p>
                        </div>
                    </div>
                </body>
            </html>
        `;

        // Email options
        const mailOptions = {
            from: process.env.EMAIL_FROM || process.env.EMAIL_USER,
            to: process.env.EMAIL_USER, // Send to the configured email
            subject: emailSubject,
            html: emailHtml,
            attachments: [
                {
                    filename: `${name}_CV_${jobTitle.replace(/\s+/g, '_')}.${cvFile.originalname.split('.').pop()}`,
                    content: cvFile.buffer,
                    contentType: cvFile.mimetype
                }
            ]
        };

        // Send email
        await transporter.sendMail(mailOptions);

        console.log(`Job application received for ${jobTitle} from ${name} (${email})`);

        res.json({
            success: true,
            message: 'Application submitted successfully'
        });

    } catch (error) {
        console.error('Error processing job application:', error);
        res.status(500).json({
            error: 'Failed to submit application. Please try again later.'
        });
    }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
    res.json({ status: 'OK', message: 'Ed-admin Jobs API is running' });
});

// Start server
app.listen(PORT, () => {
    console.log(`Ed-admin Jobs API server running on port ${PORT}`);
    console.log(`Health check: http://localhost:${PORT}/api/health`);
    console.log(`Jobs API: http://localhost:${PORT}/api/jobs`);
});

// Graceful shutdown
process.on('SIGINT', () => {
    console.log('\nShutting down server...');
    db.close((err) => {
        if (err) {
            console.error('Error closing database:', err);
        } else {
            console.log('Database connection closed.');
        }
        process.exit(0);
    });
});