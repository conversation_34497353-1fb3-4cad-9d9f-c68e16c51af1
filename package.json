{"name": "edadmin-website", "version": "1.0.0", "description": "Ed-admin website with job listings backend", "main": "backend/index.js", "dependencies": {"cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "multer": "^2.0.1", "nodemailer": "^7.0.3", "sqlite3": "^5.1.6"}, "devDependencies": {"sass": "^1.77.8"}, "scripts": {"start": "node backend/index.js", "dev": "node backend/index.js", "sass": "sass assets/css/style.scss assets/css/style.css --watch"}}